#!/usr/bin/env python3
"""
Comprehensive script to fix all indentation issues in the _execute_file method
"""

import re
from pathlib import Path

def fix_method_indentation():
    """Fix all indentation issues in the _execute_file method"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create backup
    backup_file = agent_file.with_suffix('.py.backup_indent')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Backup created: {backup_file}")
    
    lines = content.split('\n')
    fixed_lines = []
    
    in_execute_file_method = False
    method_indent_level = 0
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Check if we're starting the _execute_file method
        if '    def _execute_file(self, args: str) -> str:' in line:
            in_execute_file_method = True
            method_indent_level = 1  # We're inside a method
            fixed_lines.append(line)
            i += 1
            continue
        
        if in_execute_file_method:
            # Check if we've reached the next method
            if (line.strip().startswith('def ') and 
                line.startswith('    def ') and
                '_execute_file' not in line):
                # We've reached the next method
                in_execute_file_method = False
                fixed_lines.append(line)
                i += 1
                continue
            
            # Check if we've reached end of class
            if (line.strip() and 
                not line.startswith(' ') and 
                not line.startswith('\t')):
                # End of class
                in_execute_file_method = False
                fixed_lines.append(line)
                i += 1
                continue
            
            # Fix indentation based on content
            if line.strip():
                # Determine the proper indentation level
                stripped = line.strip()
                
                # Base method content (docstring, comments, basic statements)
                if (stripped.startswith('"""') or 
                    stripped.startswith('Args:') or 
                    stripped.startswith('Returns:') or
                    stripped.startswith('# ') or
                    stripped == '"""'):
                    # 8 spaces for method content
                    fixed_line = '        ' + stripped
                    
                # Variable assignments and basic statements
                elif ('=' in stripped and not any(kw in stripped for kw in ['if ', 'elif ', 'else:', 'try:', 'except', 'for ', 'while ', 'with '])):
                    # 8 spaces for method content
                    fixed_line = '        ' + stripped
                    
                # Control flow statements (if, elif, else, try, except, etc.)
                elif (stripped.startswith('if ') or 
                      stripped.startswith('elif ') or 
                      stripped.startswith('else:') or
                      stripped.startswith('try:') or
                      stripped.startswith('except') or
                      stripped.startswith('for ') or
                      stripped.startswith('while ') or
                      stripped.startswith('with ') or
                      stripped.startswith('finally:')):
                    # 8 spaces for method content
                    fixed_line = '        ' + stripped
                    
                # Content inside control blocks (return, function calls, etc.)
                elif (stripped.startswith('return ') or
                      stripped.startswith('print(') or
                      stripped.startswith('self.') or
                      '(' in stripped or
                      stripped.startswith('content') or
                      stripped.startswith('file_path') or
                      stripped.startswith('results') or
                      stripped.startswith('path_obj')):
                    # 12 spaces for content inside control blocks
                    fixed_line = '            ' + stripped
                    
                # Comments inside blocks
                elif stripped.startswith('#'):
                    # 12 spaces for comments inside blocks
                    fixed_line = '            ' + stripped
                    
                else:
                    # Default to 8 spaces for method content
                    fixed_line = '        ' + stripped
                
                print(f"Fixed line {i+1}: {stripped}")
                fixed_lines.append(fixed_line)
            else:
                # Empty line, keep as is
                fixed_lines.append(line)
            
            i += 1
            continue
        
        # Not in the method, keep line as is
        fixed_lines.append(line)
        i += 1
    
    # Write the fixed content
    fixed_content = '\n'.join(fixed_lines)
    
    # Verify syntax
    try:
        compile(fixed_content, 'agent.py', 'exec')
        print("✓ Fixed content has valid syntax")
    except SyntaxError as e:
        print(f"✗ Fixed content still has syntax error: {e}")
        print(f"Error at line {e.lineno}: {e.text}")
        return False
    
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Successfully fixed all indentation issues in _execute_file method")
    return True

if __name__ == "__main__":
    success = fix_method_indentation()
    if success:
        print("✓ Successfully fixed method indentation")
    else:
        print("✗ Failed to fix method indentation")
