#!/usr/bin/env python3
"""
Test script for Model Integration functionality.
"""

import os
import sys
import time
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_model_imports():
    """Test model-related imports."""
    print("Testing model imports...")
    
    try:
        from models import ModelManager
        print("✓ ModelManager import successful")
    except Exception as e:
        print(f"✗ ModelManager import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✓ Google Generative AI import successful")
    except Exception as e:
        print(f"✗ Google Generative AI import failed: {e}")
        return False
    
    return True

def test_model_manager_creation():
    """Test ModelManager creation without API key."""
    print("\nTesting ModelManager creation...")
    
    try:
        from models import ModelManager
        
        # Test creation without API key
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        print("✓ ModelManager created successfully")
        print(f"  - Provider: {model_manager.provider}")
        print(f"  - Model: {model_manager.model_name}")
        print(f"  - Temperature: {model_manager.temperature}")
        print(f"  - Max tokens: {model_manager.max_tokens}")
        
        return True
    except Exception as e:
        print(f"✗ ModelManager creation failed: {e}")
        traceback.print_exc()
        return False

def test_model_manager_with_fake_key():
    """Test ModelManager with a fake API key."""
    print("\nTesting ModelManager with fake API key...")
    
    try:
        from models import ModelManager
        
        # Set a fake API key
        os.environ['GEMINI_API_KEY'] = 'fake_key_for_testing'
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096,
            api_key="fake_key_for_testing"
        )
        
        print("✓ ModelManager created with fake API key")
        
        # Clean up
        if 'GEMINI_API_KEY' in os.environ:
            del os.environ['GEMINI_API_KEY']
        
        return True
    except Exception as e:
        print(f"✗ ModelManager with fake key failed: {e}")
        return False

def test_model_manager_methods():
    """Test ModelManager methods."""
    print("\nTesting ModelManager methods...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Test method existence
        methods_to_check = [
            'generate_response',
            'generate_streaming_response',
            'set_api_key',
            'get_model_info'
        ]
        
        for method_name in methods_to_check:
            if hasattr(model_manager, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
        
        return True
    except Exception as e:
        print(f"✗ ModelManager methods test failed: {e}")
        return False

def test_model_response_without_key():
    """Test model response generation without API key."""
    print("\nTesting model response without API key...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Try to generate a response (should fail gracefully)
        try:
            response = model_manager.generate_response("Hello, test message")
            if response:
                print(f"? Unexpected response: {response[:100]}...")
            else:
                print("✓ No response without API key (expected)")
        except Exception as e:
            print(f"✓ Expected error without API key: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Model response test failed: {e}")
        return False

def test_model_streaming_without_key():
    """Test model streaming response without API key."""
    print("\nTesting model streaming without API key...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Try to generate a streaming response (should fail gracefully)
        try:
            response_gen = model_manager.generate_streaming_response("Hello, test message")
            chunks = list(response_gen)
            if chunks:
                print(f"? Unexpected streaming response: {len(chunks)} chunks")
            else:
                print("✓ No streaming response without API key (expected)")
        except Exception as e:
            print(f"✓ Expected error in streaming without API key: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Model streaming test failed: {e}")
        return False

def test_model_configuration():
    """Test model configuration options."""
    print("\nTesting model configuration...")
    
    try:
        from models import ModelManager
        
        # Test different configurations
        configs = [
            {"provider": "gemini", "model_name": "gemini-2.0-flash", "temperature": 0.1},
            {"provider": "gemini", "model_name": "gemini-2.0-flash", "temperature": 0.9},
            {"provider": "gemini", "model_name": "gemini-2.0-flash", "max_tokens": 1024},
            {"provider": "gemini", "model_name": "gemini-2.0-flash", "max_tokens": 8192},
        ]
        
        for i, config in enumerate(configs):
            try:
                model_manager = ModelManager(**config)
                print(f"✓ Configuration {i+1} works: {config}")
            except Exception as e:
                print(f"✗ Configuration {i+1} failed: {config} - {e}")
        
        return True
    except Exception as e:
        print(f"✗ Model configuration test failed: {e}")
        return False

def test_model_info():
    """Test model info retrieval."""
    print("\nTesting model info...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Try to get model info
        if hasattr(model_manager, 'get_model_info'):
            try:
                info = model_manager.get_model_info()
                if info:
                    print(f"✓ Model info retrieved: {info}")
                else:
                    print("✓ No model info (expected without API key)")
            except Exception as e:
                print(f"✓ Expected error getting model info: {type(e).__name__}")
        else:
            print("? get_model_info method not found")
        
        return True
    except Exception as e:
        print(f"✗ Model info test failed: {e}")
        return False

def main():
    """Run all model integration tests."""
    print("=" * 60)
    print("MODEL INTEGRATION TESTING")
    print("=" * 60)
    
    tests = [
        test_model_imports,
        test_model_manager_creation,
        test_model_manager_with_fake_key,
        test_model_manager_methods,
        test_model_response_without_key,
        test_model_streaming_without_key,
        test_model_configuration,
        test_model_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All model integration tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
