#!/usr/bin/env python3
"""
Final script to fix agent.py indentation and structure issues
"""

import ast
import re
from pathlib import Path

def fix_agent_final():
    """Fix agent.py with a comprehensive approach"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create backup
    backup_file = agent_file.with_suffix('.py.backup_final')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Backup created: {backup_file}")
    
    lines = content.split('\n')
    fixed_lines = []
    
    in_agent_class = False
    class_indent = 0
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Check if we're starting the Agent class
        if line.strip().startswith('class Agent:'):
            in_agent_class = True
            class_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
            i += 1
            continue
        
        # If we're in the Agent class
        if in_agent_class:
            # Check if this line should end the class
            if (line.strip() and 
                not line.startswith(' ') and 
                not line.startswith('\t') and
                not line.strip().startswith('#')):
                # This line is at the top level, class should end
                in_agent_class = False
                fixed_lines.append(line)
                i += 1
                continue
            
            # Handle method definitions
            if re.match(r'\s*def ', line):
                # This is a method definition
                if not line.startswith('    def '):
                    # Fix method definition indentation
                    method_name = re.search(r'def\s+(\w+)', line).group(1)
                    line = '    def ' + line.strip()[4:]  # Remove 'def ' and add proper indent
                    print(f"Fixed method definition: {method_name}")
                
                fixed_lines.append(line)
                i += 1
                
                # Now fix the method content
                method_indent = 4  # Methods are indented 4 spaces from class
                while i < len(lines):
                    content_line = lines[i]
                    
                    # Check if we've reached the next method or end of class
                    if (re.match(r'\s*def ', content_line) or
                        (content_line.strip() and not content_line.startswith(' ') and not content_line.startswith('\t'))):
                        # Next method or end of class, break to handle it
                        break
                    
                    # Fix method content indentation
                    if content_line.strip():  # Non-empty line
                        if not content_line.startswith('        '):  # Should be 8 spaces for method content
                            content_line = '        ' + content_line.lstrip()
                    
                    fixed_lines.append(content_line)
                    i += 1
                
                continue
            
            # Regular class content (not a method definition)
            fixed_lines.append(line)
            i += 1
            continue
        
        # Not in Agent class, keep line as is
        fixed_lines.append(line)
        i += 1
    
    # Write the fixed content
    fixed_content = '\n'.join(fixed_lines)
    
    # Verify the fix by parsing
    try:
        ast.parse(fixed_content)
        print("✓ Fixed content has valid syntax")
    except SyntaxError as e:
        print(f"✗ Fixed content still has syntax error: {e}")
        print(f"Error at line {e.lineno}: {e.text}")
        return False
    
    # Write the fixed file
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Successfully fixed agent.py")
    return True

if __name__ == "__main__":
    success = fix_agent_final()
    if success:
        print("✓ Successfully fixed agent.py structure")
    else:
        print("✗ Failed to fix agent.py structure")
