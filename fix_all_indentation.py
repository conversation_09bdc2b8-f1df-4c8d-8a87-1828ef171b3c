#!/usr/bin/env python3
"""
Script to fix all indentation issues in agent.py
"""

import re
from pathlib import Path

def fix_all_indentation():
    """Fix all indentation issues in agent.py"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create backup
    backup_file = agent_file.with_suffix('.py.backup2')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Backup created: {backup_file}")
    
    lines = content.split('\n')
    fixed_lines = []
    
    in_class = False
    in_method = False
    method_indent = 0
    
    for i, line in enumerate(lines):
        original_line = line
        
        # Check if we're starting the Agent class
        if line.strip().startswith('class Agent:'):
            in_class = True
            fixed_lines.append(line)
            continue
        
        # If we're in the class
        if in_class:
            # Check for method definitions
            if re.match(r'\s*def ', line):
                # This is a method definition
                if not line.startswith('    def '):
                    # Fix method definition indentation
                    line = '    def ' + line.strip()[4:]  # Remove 'def ' and add proper indent
                    print(f"Fixed method definition at line {i+1}: {line.strip()}")
                
                in_method = True
                method_indent = 4  # Methods should be indented 4 spaces from class
                fixed_lines.append(line)
                continue
            
            # If we're in a method, fix content indentation
            if in_method:
                if line.strip() == '':
                    # Empty line, keep as is
                    fixed_lines.append(line)
                    continue
                
                # Check if this line should end the method (next method or class)
                if (line.strip().startswith('def ') or 
                    line.strip().startswith('class ') or
                    (line.strip() and not line.startswith(' ') and not line.startswith('\t'))):
                    in_method = False
                    # This line will be processed in the next iteration
                
                if in_method:
                    # This should be method content, ensure proper indentation
                    if line.strip():
                        # Non-empty line that should be indented as method content
                        if not line.startswith('        '):  # 8 spaces for method content
                            line = '        ' + line.lstrip()
                            if original_line != line:
                                print(f"Fixed content indentation at line {i+1}: {line.strip()}")
                    
                    fixed_lines.append(line)
                    continue
        
        # Default: keep line as is
        fixed_lines.append(line)
    
    # Write the fixed content
    fixed_content = '\n'.join(fixed_lines)
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed all indentation issues in agent.py")
    return True

if __name__ == "__main__":
    success = fix_all_indentation()
    if success:
        print("✓ Successfully fixed all indentation")
    else:
        print("✗ Failed to fix indentation")
