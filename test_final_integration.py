#!/usr/bin/env python3
"""
Final Integration Test - Comprehensive end-to-end testing.
"""

import os
import sys
import time
import traceback
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_complete_workflow():
    """Test complete workflow from CLI to response."""
    print("Testing complete workflow...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create components
            model_manager = ModelManager()
            conv_manager = ConversationManager(temp_path)
            agent = Agent(model_manager, conv_manager)
            
            print("✓ All components created successfully")
            
            # Test conversation creation
            conversation = conv_manager.new_conversation("Integration Test")
            print(f"✓ Conversation created: {conversation.name}")
            
            # Test message processing (without API key, should handle gracefully)
            test_message = "Hello, this is an integration test"
            try:
                response = agent.process_message(test_message)
                print("✓ Message processed (no API key expected)")
            except Exception as e:
                print(f"? Message processing error (expected): {type(e).__name__}")
            
            # Test conversation saving
            conv_manager.save_conversation()
            print("✓ Conversation saved")
            
            # Test conversation loading
            conversations = conv_manager.list_conversations()
            if conversations:
                print(f"✓ Conversations loaded: {len(conversations)} found")
            else:
                print("? No conversations found after save")
            
            return True
    except Exception as e:
        print(f"✗ Complete workflow test failed: {e}")
        traceback.print_exc()
        return False

def test_all_tools_integration():
    """Test integration of all tools."""
    print("\nTesting all tools integration...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        tools = agent.tools
        print(f"✓ Agent has {len(tools)} tools available")
        
        # Test a few key tools
        key_tools = ['file_read', 'shell', 'code_execute']
        for tool_name in key_tools:
            if tool_name in tools:
                print(f"✓ {tool_name} tool available and accessible")
            else:
                print(f"✗ {tool_name} tool missing")
        
        # Test file operations workflow
        test_file = Path('integration_test.txt')
        try:
            # Write file
            if 'file_write' in tools:
                result = agent._file_write(str(test_file), "Integration test content")
                print("✓ File write operation works")
            
            # Read file
            if 'file_read' in tools:
                result = agent._file_read(str(test_file))
                if "Integration test content" in result:
                    print("✓ File read operation works")
                else:
                    print(f"? File read result: {result[:50]}...")
            
            # Delete file
            if 'file_delete' in tools:
                result = agent._file_delete(str(test_file))
                print("✓ File delete operation works")
        
        finally:
            # Clean up
            if test_file.exists():
                test_file.unlink()
        
        return True
    except Exception as e:
        print(f"✗ All tools integration test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration_integration():
    """Test configuration system integration."""
    print("\nTesting configuration integration...")
    
    try:
        from config import load_config, save_config
        from models import ModelManager
        
        # Load config
        config = load_config()
        print("✓ Configuration loaded")
        
        # Test model manager with config
        model_manager = ModelManager(
            provider=config.agent.provider,
            model_name=config.agent.model,
            temperature=config.agent.temperature,
            max_tokens=config.agent.max_tokens
        )
        print("✓ Model manager created with config")
        
        # Test config modification and save
        original_temp = config.agent.temperature
        config.agent.temperature = 0.8
        save_config(config)
        
        # Restore original
        config.agent.temperature = original_temp
        save_config(config)
        print("✓ Configuration save/restore works")
        
        return True
    except Exception as e:
        print(f"✗ Configuration integration test failed: {e}")
        traceback.print_exc()
        return False

def test_cli_integration():
    """Test CLI integration."""
    print("\nTesting CLI integration...")
    
    try:
        import subprocess
        
        # Test CLI help
        result = subprocess.run(
            [sys.executable, 'cli.py', '--help'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✓ CLI help command works")
        else:
            print(f"? CLI help returned code {result.returncode}")
        
        # Test CLI version
        result = subprocess.run(
            [sys.executable, 'cli.py', '--version'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✓ CLI version command works")
        else:
            print(f"? CLI version returned code {result.returncode}")
        
        return True
    except Exception as e:
        print(f"✗ CLI integration test failed: {e}")
        traceback.print_exc()
        return False

def test_core_components_integration():
    """Test core components integration."""
    print("\nTesting core components integration...")
    
    try:
        from core.ai_code_assistant import AICodeAssistant
        from core.execution_monitor import ExecutionMonitor
        from models import ModelManager
        
        # Test AI Code Assistant integration
        model_manager = ModelManager()
        ai_assistant = AICodeAssistant(model_manager, Path('.'))
        print("✓ AI Code Assistant integrates with ModelManager")
        
        # Test Execution Monitor
        monitor = ExecutionMonitor()
        print("✓ Execution Monitor created")
        
        return True
    except Exception as e:
        print(f"✗ Core components integration test failed: {e}")
        traceback.print_exc()
        return False

def test_performance_under_load():
    """Test system performance under load."""
    print("\nTesting performance under load...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create components
            model_manager = ModelManager()
            conv_manager = ConversationManager(temp_path)
            agent = Agent(model_manager, conv_manager)
            
            # Test multiple rapid operations
            start_time = time.time()
            
            for i in range(20):
                conv = conv_manager.new_conversation(f"Load Test {i}")
                conv.add_message("user", f"Test message {i}")
                conv.add_message("assistant", f"Test response {i}")
                conv_manager.save_conversation()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✓ Created 20 conversations in {duration:.2f} seconds")
            
            # Test tool operations
            start_time = time.time()
            
            for i in range(10):
                result = agent._file_list(".")
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✓ Performed 10 file operations in {duration:.2f} seconds")
            
            return True
    except Exception as e:
        print(f"✗ Performance under load test failed: {e}")
        traceback.print_exc()
        return False

def test_system_recovery():
    """Test system recovery from errors."""
    print("\nTesting system recovery...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test recovery from file operation errors
        try:
            agent._file_read("non_existent_file.txt")
            agent._file_read("README.md")  # Should work after error
            print("✓ System recovers from file operation errors")
        except Exception as e:
            print(f"? System recovery issue: {type(e).__name__}")
        
        # Test recovery from tool execution errors
        try:
            if 'shell' in agent.tools:
                agent.tools['shell']('invalid_command')
                agent.tools['shell']('echo "recovery test"')  # Should work after error
            print("✓ System recovers from tool execution errors")
        except Exception as e:
            print(f"? Tool recovery issue: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ System recovery test failed: {e}")
        traceback.print_exc()
        return False

def test_final_validation():
    """Final validation of all critical components."""
    print("\nRunning final validation...")
    
    try:
        # Test all critical imports
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager, Conversation, Message
        from config import load_config, save_config
        from tools.tool_manager import ToolManager
        from core.ai_code_assistant import AICodeAssistant
        print("✓ All critical imports successful")
        
        # Test basic functionality
        config = load_config()
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        print("✓ All components instantiate correctly")
        print(f"✓ Agent has {len(agent.tools)} tools")
        print(f"✓ Model: {config.agent.model}")
        print(f"✓ Provider: {config.agent.provider}")
        
        return True
    except Exception as e:
        print(f"✗ Final validation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all final integration tests."""
    print("=" * 60)
    print("FINAL INTEGRATION TESTING")
    print("=" * 60)
    
    tests = [
        test_complete_workflow,
        test_all_tools_integration,
        test_configuration_integration,
        test_cli_integration,
        test_core_components_integration,
        test_performance_under_load,
        test_system_recovery,
        test_final_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("🚀 System is ready for production use!")
        return True
    else:
        print("❌ Some integration tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
