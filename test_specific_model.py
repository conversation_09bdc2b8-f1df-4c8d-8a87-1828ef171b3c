#!/usr/bin/env python3
"""
Specific test for ModelManager issue.
"""

import sys
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_model_manager_methods():
    """Test ModelManager methods."""
    print("Testing ModelManager methods...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Test method existence
        methods_to_check = [
            'generate_response',
            'generate_streaming_response',
            'set_api_key',
            'get_model_info'
        ]
        
        for method_name in methods_to_check:
            if hasattr(model_manager, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
        
        return True
    except Exception as e:
        print(f"✗ ModelManager methods test failed: {e}")
        traceback.print_exc()
        return False

def test_model_response_without_key():
    """Test model response generation without API key."""
    print("\nTesting model response without API key...")
    
    try:
        from models import ModelManager
        
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        # Try to generate a response (should fail gracefully)
        try:
            response = model_manager.generate_response("Hello, test message")
            if response:
                print(f"? Unexpected response: {response[:100]}...")
            else:
                print("✓ No response without API key (expected)")
        except Exception as e:
            print(f"✓ Expected error without API key: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Model response test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model_manager_methods()
    test_model_response_without_key()
