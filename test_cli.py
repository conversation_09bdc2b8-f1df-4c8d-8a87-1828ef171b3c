#!/usr/bin/env python3
"""
Test script for CLI functionality.
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(cmd, timeout=30):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def test_cli_help():
    """Test CLI help command."""
    print("Testing CLI help...")
    
    returncode, stdout, stderr = run_command("python cli.py --help")
    
    if returncode == 0:
        print("✓ CLI help command works")
        if "Advanced AI Agent" in stdout:
            print("✓ Help text contains expected content")
        else:
            print("? Help text might be incomplete")
        return True
    else:
        print(f"✗ CLI help failed: {stderr}")
        return False

def test_cli_version():
    """Test CLI version command."""
    print("\nTesting CLI version...")
    
    returncode, stdout, stderr = run_command("python cli.py --version")
    
    if returncode == 0:
        print("✓ CLI version command works")
        if "Advanced AI Agent" in stdout:
            print("✓ Version output contains expected content")
        else:
            print("? Version output might be incomplete")
        return True
    else:
        print(f"✗ CLI version failed: {stderr}")
        return False

def test_cli_basic_message():
    """Test CLI with a basic message."""
    print("\nTesting CLI with basic message...")
    
    returncode, stdout, stderr = run_command('python cli.py "Hello, this is a test"', timeout=10)
    
    if returncode == 0:
        print("✓ CLI basic message command works")
        if stdout.strip():
            print(f"✓ Got output: {stdout[:100]}...")
        else:
            print("? No output (likely due to missing API key)")
        return True
    else:
        print(f"✗ CLI basic message failed: {stderr}")
        return False

def test_cli_with_options():
    """Test CLI with various options."""
    print("\nTesting CLI with options...")
    
    # Test with debug flag
    returncode, stdout, stderr = run_command('python cli.py --debug "Test with debug"', timeout=10)
    
    if returncode == 0:
        print("✓ CLI with --debug flag works")
        return True
    else:
        print(f"✗ CLI with --debug failed: {stderr}")
        return False

def test_cli_workspace_option():
    """Test CLI with workspace option."""
    print("\nTesting CLI with workspace option...")
    
    returncode, stdout, stderr = run_command('python cli.py --workspace . "Test workspace"', timeout=10)
    
    if returncode == 0:
        print("✓ CLI with --workspace option works")
        return True
    else:
        print(f"✗ CLI with --workspace failed: {stderr}")
        return False

def test_cli_model_option():
    """Test CLI with model option."""
    print("\nTesting CLI with model option...")
    
    returncode, stdout, stderr = run_command('python cli.py --model gemini-2.0-flash "Test model"', timeout=10)
    
    if returncode == 0:
        print("✓ CLI with --model option works")
        return True
    else:
        print(f"✗ CLI with --model failed: {stderr}")
        return False

def test_cli_import():
    """Test if CLI can be imported."""
    print("\nTesting CLI import...")
    
    try:
        import cli
        print("✓ CLI module can be imported")
        return True
    except Exception as e:
        print(f"✗ CLI import failed: {e}")
        return False

def main():
    """Run all CLI tests."""
    print("=" * 60)
    print("CLI TESTING")
    print("=" * 60)
    
    tests = [
        test_cli_import,
        test_cli_help,
        test_cli_version,
        test_cli_basic_message,
        test_cli_with_options,
        test_cli_workspace_option,
        test_cli_model_option
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All CLI tests passed!")
        return True
    else:
        print("❌ Some CLI tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
