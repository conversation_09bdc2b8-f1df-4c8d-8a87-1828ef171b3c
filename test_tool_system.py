#!/usr/bin/env python3
"""
Test script for Tool System functionality.
"""

import os
import sys
import time
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_tool_manager():
    """Test ToolManager functionality."""
    print("Testing ToolManager...")
    
    try:
        from tools.tool_manager import ToolManager, Tool
        
        # Create tool manager
        tool_manager = ToolManager()
        print("✓ ToolManager created successfully")
        
        # Test registering a simple tool
        def test_function(args):
            return f"Test result: {args}"
        
        test_tool = Tool(
            name="test_tool",
            description="A test tool",
            function=test_function,
            parameters={
                "type": "object",
                "properties": {
                    "input": {"type": "string", "description": "Test input"}
                },
                "required": ["input"]
            }
        )
        
        tool_manager.register_tool(test_tool)
        print("✓ Tool registered successfully")
        
        # Test getting all tools
        all_tools = tool_manager.get_all_tools()
        print(f"✓ Retrieved {len(all_tools)} tools")
        
        # Test executing tool
        result = tool_manager.execute_tool("test_tool", "hello")
        print(f"✓ Tool executed: {result}")
        
        return True
    except Exception as e:
        print(f"✗ ToolManager test failed: {e}")
        traceback.print_exc()
        return False

def test_agent_tools():
    """Test Agent tools integration."""
    print("\nTesting Agent tools integration...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        
        # Create agent
        agent = Agent(model_manager, conv_manager)
        
        # Test tools property
        tools = agent.tools
        print(f"✓ Agent has {len(tools)} tools available")
        
        # Test some specific tools
        expected_tools = ['shell', 'file_read', 'code_execute', 'web_search', 'web_fetch']
        for tool_name in expected_tools:
            if tool_name in tools:
                print(f"✓ Tool '{tool_name}' available")
            else:
                print(f"✗ Tool '{tool_name}' missing")
        
        return True
    except Exception as e:
        print(f"✗ Agent tools test failed: {e}")
        traceback.print_exc()
        return False

def test_file_tools():
    """Test file-related tools."""
    print("\nTesting file tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test file_read tool
        if 'file_read' in agent.tools:
            # Create a test file
            test_file = Path('test_file.txt')
            test_file.write_text('Hello, this is a test file!')
            
            try:
                result = agent.tools['file_read'](str(test_file))
                if 'Hello, this is a test file!' in result:
                    print("✓ file_read tool works")
                else:
                    print(f"? file_read result: {result}")
            finally:
                # Clean up
                if test_file.exists():
                    test_file.unlink()
        else:
            print("✗ file_read tool not available")
        
        return True
    except Exception as e:
        print(f"✗ File tools test failed: {e}")
        traceback.print_exc()
        return False

def test_shell_tools():
    """Test shell-related tools."""
    print("\nTesting shell tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test shell tool
        if 'shell' in agent.tools:
            # Test a simple command
            result = agent.tools['shell']('echo "Hello World"')
            if 'Hello World' in result:
                print("✓ shell tool works")
            else:
                print(f"? shell result: {result}")
        else:
            print("✗ shell tool not available")
        
        return True
    except Exception as e:
        print(f"✗ Shell tools test failed: {e}")
        traceback.print_exc()
        return False

def test_code_tools():
    """Test code execution tools."""
    print("\nTesting code tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test code_execute tool
        if 'code_execute' in agent.tools:
            # Test Python code execution
            result = agent.tools['code_execute']('python\nprint("Hello from Python")')
            if 'Hello from Python' in result:
                print("✓ code_execute tool works")
            else:
                print(f"? code_execute result: {result}")
        else:
            print("✗ code_execute tool not available")
        
        return True
    except Exception as e:
        print(f"✗ Code tools test failed: {e}")
        traceback.print_exc()
        return False

def test_web_tools():
    """Test web-related tools."""
    print("\nTesting web tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test web tools availability
        web_tools = ['web_search', 'web_fetch']
        for tool_name in web_tools:
            if tool_name in agent.tools:
                print(f"✓ {tool_name} tool available")
            else:
                print(f"✗ {tool_name} tool missing")
        
        # Note: We don't actually test web tools execution as they require internet
        print("? Web tools execution not tested (requires internet)")
        
        return True
    except Exception as e:
        print(f"✗ Web tools test failed: {e}")
        traceback.print_exc()
        return False

def test_codebase_tools():
    """Test codebase analysis tools."""
    print("\nTesting codebase tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test codebase tools availability
        codebase_tools = ['codebase_find_files', 'codebase_find_code_files', 'codebase_search', 'codebase_analyze']
        for tool_name in codebase_tools:
            if tool_name in agent.tools:
                print(f"✓ {tool_name} tool available")
            else:
                print(f"✗ {tool_name} tool missing")
        
        # Test codebase_find_files
        if 'codebase_find_files' in agent.tools:
            result = agent.tools['codebase_find_files']('*.py')
            if 'agent.py' in result:
                print("✓ codebase_find_files works")
            else:
                print(f"? codebase_find_files result: {result[:100]}...")
        
        return True
    except Exception as e:
        print(f"✗ Codebase tools test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tool system tests."""
    print("=" * 60)
    print("TOOL SYSTEM TESTING")
    print("=" * 60)
    
    tests = [
        test_tool_manager,
        test_agent_tools,
        test_file_tools,
        test_shell_tools,
        test_code_tools,
        test_web_tools,
        test_codebase_tools
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tool system tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
