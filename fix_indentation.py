#!/usr/bin/env python3
"""
Script to fix indentation issues in agent.py
"""

from pathlib import Path

def fix_indentation():
    """Fix indentation issues in agent.py"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find where the class ends and methods are incorrectly outside
    fixed_lines = []
    in_class = False
    class_indent = 0
    
    for i, line in enumerate(lines):
        # Check if we're starting the Agent class
        if line.strip().startswith('class Agent:'):
            in_class = True
            class_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
            continue
        
        # If we're in the class, check for methods that should be inside
        if in_class:
            # Check if this is a method definition that should be inside the class
            if (line.strip().startswith('def ') and 
                not line.startswith('    def ') and 
                ('_execute_' in line or '_register_' in line or 'def ' in line)):
                
                # This method should be inside the class, fix its indentation
                print(f"Fixing indentation for line {i+1}: {line.strip()}")
                # Add proper class method indentation (4 spaces)
                fixed_line = '    ' + line.lstrip()
                fixed_lines.append(fixed_line)
                continue
            
            # Fix indentation for method content
            if line.strip() and not line.startswith('    ') and not line.startswith('#') and not line.strip().startswith('"""'):
                # This line should be indented as part of a method
                if (line.startswith('    ') or line.startswith('\t') or 
                    line.strip().startswith('"""') or line.strip().startswith('Args:') or
                    line.strip().startswith('Returns:') or line.strip().startswith('try:') or
                    line.strip().startswith('except') or line.strip().startswith('if ') or
                    line.strip().startswith('else:') or line.strip().startswith('elif ') or
                    line.strip().startswith('for ') or line.strip().startswith('while ') or
                    line.strip().startswith('return ') or line.strip().startswith('with ') or
                    '=' in line or line.strip().startswith('#')):
                    
                    # Add proper method content indentation (8 spaces)
                    if not line.startswith('        '):
                        print(f"Fixing content indentation for line {i+1}: {line.strip()}")
                        fixed_line = '        ' + line.lstrip()
                        fixed_lines.append(fixed_line)
                        continue
        
        # Keep the line as is
        fixed_lines.append(line)
    
    # Write the fixed content back
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed indentation in agent.py")
    return True

if __name__ == "__main__":
    success = fix_indentation()
    if success:
        print("✓ Successfully fixed indentation")
    else:
        print("✗ Failed to fix indentation")
