#!/usr/bin/env python3
"""
Debug script to check Agent methods
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def debug_agent_methods():
    """Debug Agent class methods"""
    
    try:
        from agent import Agent
        print("✓ Agent import successful")
        
        # Check if the class has the method
        if hasattr(Agent, '_register_scrape_tools'):
            print("✓ _register_scrape_tools method exists in class")
        else:
            print("✗ _register_scrape_tools method NOT found in class")
            
        # List all methods that start with _register
        register_methods = [method for method in dir(Agent) if method.startswith('_register')]
        print(f"\nFound {len(register_methods)} _register methods:")
        for method in sorted(register_methods):
            print(f"  - {method}")
            
        # Try to create an instance without calling _initialize_tools
        print("\nTrying to create Agent instance...")
        
        from models import ModelManager
        from conversation import ConversationManager
        from config import load_config
        
        config = load_config()
        
        model_manager = ModelManager(
            provider=config.agent.provider,
            model_name=config.agent.model,
            temperature=config.agent.temperature,
            max_tokens=config.agent.max_tokens
        )
        
        conv_manager = ConversationManager(config.history_dir)
        
        # Create agent instance but skip initialization
        agent = object.__new__(Agent)
        agent.model_manager = model_manager
        agent.conversation_manager = conv_manager
        agent.workspace_dir = config.workspace_dir or Path.cwd()
        
        # Check if method exists on instance
        if hasattr(agent, '_register_scrape_tools'):
            print("✓ _register_scrape_tools method exists on instance")
        else:
            print("✗ _register_scrape_tools method NOT found on instance")
            
        # Try to call the method directly
        try:
            method = getattr(agent, '_register_scrape_tools', None)
            if method:
                print("✓ Method can be retrieved")
                print(f"  Method type: {type(method)}")
            else:
                print("✗ Method cannot be retrieved")
        except Exception as e:
            print(f"✗ Error retrieving method: {e}")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_agent_methods()
