#!/usr/bin/env python3
"""
Test script for Conversation Management functionality.
"""

import os
import sys
import time
import traceback
import tempfile
import shutil
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_conversation_imports():
    """Test conversation imports."""
    print("Testing conversation imports...")
    
    try:
        from conversation import ConversationManager, Conversation, Message
        print("✓ All conversation imports successful")
        return True
    except Exception as e:
        print(f"✗ Conversation imports failed: {e}")
        traceback.print_exc()
        return False

def test_message_class():
    """Test Message class functionality."""
    print("\nTesting Message class...")
    
    try:
        from conversation import Message
        
        # Create a message
        message = Message(role="user", content="Hello, world!")
        print("✓ Message created successfully")
        
        # Test message attributes
        print(f"✓ Message role: {message.role}")
        print(f"✓ Message content: {message.content}")
        print(f"✓ Message timestamp: {message.timestamp}")
        
        # Test different roles
        roles = ["user", "assistant", "system", "tool"]
        for role in roles:
            msg = Message(role=role, content=f"Test {role} message")
            print(f"✓ {role} message created")
        
        return True
    except Exception as e:
        print(f"✗ Message class test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_class():
    """Test Conversation class functionality."""
    print("\nTesting Conversation class...")
    
    try:
        from conversation import Conversation, Message
        
        # Create a conversation
        conversation = Conversation(name="Test Conversation")
        print("✓ Conversation created successfully")

        # Test conversation attributes
        print(f"✓ Conversation ID: {conversation.id}")
        print(f"✓ Conversation name: {conversation.name}")
        print(f"✓ Conversation created_at: {conversation.created_at}")
        
        # Test adding messages
        conversation.add_message("user", "Hello!")
        conversation.add_message("assistant", "Hi there!")
        print(f"✓ Messages added: {len(conversation.messages)} messages")
        
        # Test getting messages
        messages = conversation.messages
        print(f"✓ Retrieved {len(messages)} messages")
        
        return True
    except Exception as e:
        print(f"✗ Conversation class test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_manager():
    """Test ConversationManager functionality."""
    print("\nTesting ConversationManager...")
    
    try:
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create conversation manager
            manager = ConversationManager(temp_path)
            print("✓ ConversationManager created successfully")
            
            # Test creating a new conversation
            conversation = manager.new_conversation("Test Conversation")
            print(f"✓ Conversation created: {conversation.name}")
            
            # Test adding messages
            conversation.add_message("user", "Hello!")
            conversation.add_message("assistant", "Hi there!")
            
            # Test saving conversation
            manager.save_conversation()
            print("✓ Conversation saved")
            
            # Test loading conversations
            conversations = manager.list_conversations()
            print(f"✓ Found {len(conversations)} conversations")
            
            return True
    except Exception as e:
        print(f"✗ ConversationManager test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_persistence():
    """Test conversation persistence."""
    print("\nTesting conversation persistence...")
    
    try:
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create first manager and conversation
            manager1 = ConversationManager(temp_path)
            conv1 = manager1.new_conversation("Persistent Test")
            conv1.add_message("user", "This should persist")
            conv1.add_message("assistant", "Yes, it should!")
            manager1.save_conversation()
            conv_id = conv1.id

            # Create second manager and load conversation
            manager2 = ConversationManager(temp_path)
            conversations = manager2.list_conversations()
            
            if conversations:
                conv_info = conversations[0]
                if conv_info['id'] == conv_id:
                    print("✓ Conversation ID persisted")
                if conv_info['name'] == "Persistent Test":
                    print("✓ Conversation name persisted")
                if conv_info['message_count'] == 2:
                    print("✓ Messages persisted")
                else:
                    print(f"? Message count: {conv_info['message_count']}")
            else:
                print("✗ No conversations found after reload")
            
            return True
    except Exception as e:
        print(f"✗ Conversation persistence test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_search():
    """Test conversation search functionality."""
    print("\nTesting conversation search...")
    
    try:
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create manager and multiple conversations
            manager = ConversationManager(temp_path)

            conv1 = manager.new_conversation("Python Discussion")
            conv1.add_message("user", "How do I use Python lists?")
            conv1.add_message("assistant", "Python lists are created with []")
            manager.save_conversation()

            conv2 = manager.new_conversation("JavaScript Help")
            conv2.add_message("user", "What is JavaScript?")
            conv2.add_message("assistant", "JavaScript is a programming language")
            manager.save_conversation()

            # Test getting all conversations
            all_conversations = manager.list_conversations()
            print(f"✓ Found {len(all_conversations)} conversations")

            # Test finding specific conversation
            for conv_info in all_conversations:
                if "Python" in conv_info['name']:
                    print("✓ Found Python conversation")
                if "JavaScript" in conv_info['name']:
                    print("✓ Found JavaScript conversation")
            
            return True
    except Exception as e:
        print(f"✗ Conversation search test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_export():
    """Test conversation export functionality."""
    print("\nTesting conversation export...")
    
    try:
        from conversation import ConversationManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create manager and conversation
            manager = ConversationManager(temp_path)
            conv = manager.new_conversation("Export Test")
            conv.add_message("user", "Test message 1")
            conv.add_message("assistant", "Test response 1")
            manager.save_conversation()
            
            # Test if conversation files are created
            conv_files = list(temp_path.glob("*.json"))
            if conv_files:
                print(f"✓ Conversation file created: {conv_files[0].name}")
                
                # Test file content
                with open(conv_files[0], 'r') as f:
                    data = json.load(f)
                    if 'id' in data and 'name' in data and 'messages' in data:
                        print("✓ Conversation file has correct structure")
                    else:
                        print(f"? Conversation file structure incomplete. Keys: {list(data.keys())}")
            else:
                print("✗ No conversation files found")
            
            return True
    except Exception as e:
        print(f"✗ Conversation export test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_with_agent():
    """Test conversation integration with agent."""
    print("\nTesting conversation with agent...")
    
    try:
        from conversation import ConversationManager
        from agent import Agent
        from models import ModelManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create components
            model_manager = ModelManager()
            conv_manager = ConversationManager(temp_path)
            agent = Agent(model_manager, conv_manager)
            
            print("✓ Agent created with conversation manager")
            
            # Test that agent has conversation manager
            if hasattr(agent, 'conversation_manager'):
                print("✓ Agent has conversation_manager attribute")
            else:
                print("✗ Agent missing conversation_manager attribute")
            
            return True
    except Exception as e:
        print(f"✗ Conversation with agent test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all conversation management tests."""
    print("=" * 60)
    print("CONVERSATION MANAGEMENT TESTING")
    print("=" * 60)
    
    tests = [
        test_conversation_imports,
        test_message_class,
        test_conversation_class,
        test_conversation_manager,
        test_conversation_persistence,
        test_conversation_search,
        test_conversation_export,
        test_conversation_with_agent
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All conversation management tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
