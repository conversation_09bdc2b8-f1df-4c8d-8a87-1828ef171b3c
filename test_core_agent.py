#!/usr/bin/env python3
"""
Test script for core Agent functionality.
"""

import os
import sys
import time
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test all core imports."""
    print("Testing imports...")
    
    try:
        from agent import Agent
        print("✓ Agent import successful")
    except Exception as e:
        print(f"✗ Agent import failed: {e}")
        return False
    
    try:
        from models import ModelManager
        print("✓ ModelManager import successful")
    except Exception as e:
        print(f"✗ ModelManager import failed: {e}")
        return False
    
    try:
        from conversation import ConversationManager
        print("✓ ConversationManager import successful")
    except Exception as e:
        print(f"✗ ConversationManager import failed: {e}")
        return False
    
    try:
        from config import load_config, save_config
        print("✓ Config imports successful")
    except Exception as e:
        print(f"✗ Config imports failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from config import load_config, save_config, get_config_dir
        
        config = load_config()
        print(f"✓ Config loaded successfully")
        print(f"  - Model: {config.agent.model}")
        print(f"  - Provider: {config.agent.provider}")
        print(f"  - Temperature: {config.agent.temperature}")
        print(f"  - Max tokens: {config.agent.max_tokens}")
        print(f"  - Debug: {config.debug}")
        print(f"  - Config dir: {get_config_dir()}")
        
        return True
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        traceback.print_exc()
        return False

def test_model_manager():
    """Test ModelManager initialization."""
    print("\nTesting ModelManager...")
    
    try:
        from models import ModelManager
        from config import load_config
        
        config = load_config()
        
        # Test without API key first
        model_manager = ModelManager(
            provider=config.agent.provider,
            model_name=config.agent.model,
            temperature=config.agent.temperature,
            max_tokens=config.agent.max_tokens
        )
        print("✓ ModelManager created successfully")
        print(f"  - Provider: {model_manager.provider}")
        print(f"  - Model: {model_manager.model_name}")
        
        return True
    except Exception as e:
        print(f"✗ ModelManager test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_manager():
    """Test ConversationManager."""
    print("\nTesting ConversationManager...")
    
    try:
        from conversation import ConversationManager
        from config import load_config
        
        config = load_config()
        
        conv_manager = ConversationManager(config.history_dir)
        print("✓ ConversationManager created successfully")
        
        # Test creating a new conversation
        conversation = conv_manager.new_conversation("Test Conversation")
        print(f"✓ New conversation created: {conversation.name}")
        print(f"  - ID: {conversation.id}")
        
        # Test adding a message
        conversation.add_message("user", "Hello, this is a test message")
        print("✓ Message added to conversation")
        
        # Test saving conversation
        conv_manager.save_conversation()
        print("✓ Conversation saved")
        
        # Test listing conversations
        conversations = conv_manager.list_conversations()
        print(f"✓ Found {len(conversations)} conversations")
        
        return True
    except Exception as e:
        print(f"✗ ConversationManager test failed: {e}")
        traceback.print_exc()
        return False

def test_agent_creation():
    """Test Agent creation."""
    print("\nTesting Agent creation...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        from config import load_config
        
        config = load_config()
        
        # Create model manager
        model_manager = ModelManager(
            provider=config.agent.provider,
            model_name=config.agent.model,
            temperature=config.agent.temperature,
            max_tokens=config.agent.max_tokens
        )
        
        # Create conversation manager
        conv_manager = ConversationManager(config.history_dir)
        
        # Create agent
        agent = Agent(
            model_manager=model_manager,
            conversation_manager=conv_manager,
            workspace_dir=config.workspace_dir
        )
        
        print("✓ Agent created successfully")
        print(f"  - Workspace: {agent.workspace_dir}")
        print(f"  - Tools available: {len(agent.tools) if hasattr(agent, 'tools') else 'Unknown'}")
        
        return True
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        traceback.print_exc()
        return False

def test_tools():
    """Test tool initialization."""
    print("\nTesting tools...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        from config import load_config
        
        config = load_config()
        
        model_manager = ModelManager(
            provider=config.agent.provider,
            model_name=config.agent.model,
            temperature=config.agent.temperature,
            max_tokens=config.agent.max_tokens
        )
        
        conv_manager = ConversationManager(config.history_dir)
        agent = Agent(model_manager, conv_manager, config.workspace_dir)
        
        # Check if tools are available
        if hasattr(agent, 'tools'):
            print(f"✓ Agent has {len(agent.tools)} tools")
            for tool_name in agent.tools.keys():
                print(f"  - {tool_name}")
        else:
            print("? Tools attribute not found")
        
        # Test individual tool access
        if hasattr(agent, 'shell_tool'):
            print("✓ Shell tool available")
        if hasattr(agent, 'file_tool'):
            print("✓ File tool available")
        if hasattr(agent, 'code_tool'):
            print("✓ Code tool available")
        if hasattr(agent, 'web_tool'):
            print("✓ Web tool available")
        
        return True
    except Exception as e:
        print(f"✗ Tools test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("CORE AGENT TESTING")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config,
        test_model_manager,
        test_conversation_manager,
        test_agent_creation,
        test_tools
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All core agent tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
