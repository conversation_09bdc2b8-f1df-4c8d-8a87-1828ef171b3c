#!/usr/bin/env python3
"""
Debug script for ModelManager issues.
"""

import sys
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def debug_config():
    """Debug config loading."""
    print("Debugging config...")
    
    try:
        from config import get_available_models, get_api_key
        
        available_models = get_available_models()
        print(f"Available models: {available_models}")
        
        if "gemini" in available_models:
            print(f"Gemini models: {available_models['gemini']}")
            print(f"First gemini model: {available_models['gemini'][0]}")
        
        try:
            api_key = get_api_key("gemini")
            print(f"API key found: {'Yes' if api_key else 'No'}")
        except Exception as e:
            print(f"API key error: {e}")
        
        return True
    except Exception as e:
        print(f"Config debug failed: {e}")
        traceback.print_exc()
        return False

def debug_gemini_model():
    """Debug GeminiModel creation."""
    print("\nDebugging GeminiModel...")
    
    try:
        from models.gemini import GeminiModel
        
        # Try to create without API key
        try:
            model = GeminiModel(
                model_name="gemini-2.0-flash",
                temperature=0.7,
                max_tokens=4096,
                api_key=None
            )
            print("✓ GeminiModel created without API key")
        except Exception as e:
            print(f"GeminiModel creation failed: {e}")
        
        # Try with fake API key
        try:
            model = GeminiModel(
                model_name="gemini-2.0-flash",
                temperature=0.7,
                max_tokens=4096,
                api_key="fake_key"
            )
            print("✓ GeminiModel created with fake API key")
        except Exception as e:
            print(f"GeminiModel with fake key failed: {e}")
        
        return True
    except Exception as e:
        print(f"GeminiModel debug failed: {e}")
        traceback.print_exc()
        return False

def debug_model_manager():
    """Debug ModelManager creation step by step."""
    print("\nDebugging ModelManager step by step...")
    
    try:
        from models import ModelManager
        from config import get_available_models
        
        print("Step 1: Getting available models...")
        available_models = get_available_models()
        print(f"Available models: {available_models}")
        
        print("Step 2: Creating ModelManager with explicit model name...")
        try:
            model_manager = ModelManager(
                provider="gemini",
                model_name="gemini-2.0-flash",
                temperature=0.7,
                max_tokens=4096
            )
            print("✓ ModelManager created with explicit model name")
        except Exception as e:
            print(f"ModelManager creation failed: {e}")
            traceback.print_exc()
            return False
        
        print("Step 3: Testing ModelManager methods...")
        methods = ['generate', 'stream_generate', 'generate_response', 'generate_streaming_response', 'set_api_key', 'get_model_info']
        for method in methods:
            if hasattr(model_manager, method):
                print(f"✓ Method {method} exists")
            else:
                print(f"✗ Method {method} missing")
        
        print("Step 4: Testing get_model_info...")
        try:
            info = model_manager.get_model_info()
            print(f"✓ Model info: {info}")
        except Exception as e:
            print(f"get_model_info failed: {e}")
        
        return True
    except Exception as e:
        print(f"ModelManager debug failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests."""
    print("=" * 60)
    print("MODEL MANAGER DEBUG")
    print("=" * 60)
    
    tests = [
        debug_config,
        debug_gemini_model,
        debug_model_manager
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
