"const http = require('http');\n\nconst PORT = 3000; // Use a constant for the port number\n\nconst jsonData = { // Use a more descriptive variable name\n  message: 'Hello from the server!',\n  items: [\n    { id: 1, name: 'Item 1' },\n    { id: 2, name: 'Item 2' },\n  ],\n};\n\nconst requestHandler = (req, res) => { // Extract the request handler into a named function\n  res.setHeader('Content-Type', 'application/json');\n  res.setHeader('Access-Control-Allow-Origin', '*'); // Allow CORS for local development\n  res.writeHead(200); // Add a status code explicitly\n  res.end(JSON.stringify(jsonData));\n};\n\nconst server = http.createServer(requestHandler);\n\nserver.listen(PORT, () => {\n  console.log(`Server running at http://localhost:${PORT}/`);\n});"