#!/usr/bin/env python3
"""
Test script for <PERSON><PERSON><PERSON> Handling and Edge Cases.
"""

import os
import sys
import time
import traceback
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_agent_with_invalid_inputs():
    """Test agent behavior with invalid inputs."""
    print("Testing agent with invalid inputs...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test with empty message
        try:
            response = agent.process_message("")
            print("✓ Empty message handled gracefully")
        except Exception as e:
            print(f"? Empty message error: {type(e).__name__}")
        
        # Test with very long message
        long_message = "A" * 10000
        try:
            response = agent.process_message(long_message)
            print("✓ Long message handled gracefully")
        except Exception as e:
            print(f"? Long message error: {type(e).__name__}")
        
        # Test with special characters
        special_message = "Test with special chars: !@#$%^&*()[]{}|\\:;\"'<>,.?/~`"
        try:
            response = agent.process_message(special_message)
            print("✓ Special characters handled gracefully")
        except Exception as e:
            print(f"? Special characters error: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Agent invalid inputs test failed: {e}")
        traceback.print_exc()
        return False

def test_file_operations_errors():
    """Test file operations error handling."""
    print("\nTesting file operations errors...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test reading non-existent file
        try:
            result = agent._file_read("non_existent_file.txt")
            if "Error" in result:
                print("✓ Non-existent file error handled")
            else:
                print(f"? Unexpected result for non-existent file: {result}")
        except Exception as e:
            print(f"? File read exception: {type(e).__name__}")
        
        # Test writing to invalid path
        try:
            result = agent._file_write("/invalid/path/file.txt", "content")
            if "Error" in result:
                print("✓ Invalid path error handled")
            else:
                print(f"? Unexpected result for invalid path: {result}")
        except Exception as e:
            print(f"? File write exception: {type(e).__name__}")
        
        # Test deleting non-existent file
        try:
            result = agent._file_delete("non_existent_file.txt")
            if "Error" in result:
                print("✓ Delete non-existent file error handled")
            else:
                print(f"? Unexpected result for delete: {result}")
        except Exception as e:
            print(f"? File delete exception: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ File operations errors test failed: {e}")
        traceback.print_exc()
        return False

def test_model_manager_errors():
    """Test model manager error handling."""
    print("\nTesting model manager errors...")
    
    try:
        from models import ModelManager
        
        # Test with invalid provider
        try:
            model_manager = ModelManager(provider="invalid_provider")
            print("? Invalid provider accepted (might have fallback)")
        except Exception as e:
            print(f"✓ Invalid provider error handled: {type(e).__name__}")
        
        # Test with invalid model name
        try:
            model_manager = ModelManager(provider="gemini", model_name="invalid_model")
            print("? Invalid model name accepted (might have fallback)")
        except Exception as e:
            print(f"✓ Invalid model name error handled: {type(e).__name__}")
        
        # Test with invalid temperature
        try:
            model_manager = ModelManager(temperature=-1.0)
            print("? Invalid temperature accepted")
        except Exception as e:
            print(f"✓ Invalid temperature error handled: {type(e).__name__}")
        
        try:
            model_manager = ModelManager(temperature=2.0)
            print("? High temperature accepted")
        except Exception as e:
            print(f"✓ High temperature error handled: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Model manager errors test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_manager_errors():
    """Test conversation manager error handling."""
    print("\nTesting conversation manager errors...")
    
    try:
        from conversation import ConversationManager
        
        # Test with invalid directory
        try:
            manager = ConversationManager(Path("/invalid/directory/path"))
            print("? Invalid directory accepted (might create)")
        except Exception as e:
            print(f"✓ Invalid directory error handled: {type(e).__name__}")
        
        # Test with read-only directory (if possible)
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create manager
            manager = ConversationManager(temp_path)
            
            # Test loading non-existent conversation
            try:
                conv = manager.load_conversation("non_existent_id")
                if conv is None:
                    print("✓ Non-existent conversation handled")
                else:
                    print(f"? Unexpected result for non-existent conversation: {conv}")
            except Exception as e:
                print(f"✓ Non-existent conversation error handled: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Conversation manager errors test failed: {e}")
        traceback.print_exc()
        return False

def test_tool_execution_errors():
    """Test tool execution error handling."""
    print("\nTesting tool execution errors...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test shell command that fails
        if 'shell' in agent.tools:
            try:
                result = agent.tools['shell']('invalid_command_that_does_not_exist')
                if "error" in result.lower() or "not found" in result.lower():
                    print("✓ Invalid shell command error handled")
                else:
                    print(f"? Unexpected shell result: {result[:100]}...")
            except Exception as e:
                print(f"✓ Shell command exception handled: {type(e).__name__}")
        
        # Test code execution with syntax error
        if 'code_execute' in agent.tools:
            try:
                result = agent.tools['code_execute']('python\nprint("unclosed string')
                if "error" in result.lower() or "syntax" in result.lower():
                    print("✓ Code syntax error handled")
                else:
                    print(f"? Unexpected code result: {result[:100]}...")
            except Exception as e:
                print(f"✓ Code execution exception handled: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Tool execution errors test failed: {e}")
        traceback.print_exc()
        return False

def test_config_errors():
    """Test configuration error handling."""
    print("\nTesting configuration errors...")
    
    try:
        from config import load_config, save_config, Config
        
        # Test loading from non-existent directory
        try:
            config = load_config()  # Should create default config
            print("✓ Config loading with defaults works")
        except Exception as e:
            print(f"? Config loading error: {type(e).__name__}")
        
        # Test saving to read-only location (if possible)
        try:
            config = Config()
            save_config(config)
            print("✓ Config saving works")
        except Exception as e:
            print(f"? Config saving error: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration errors test failed: {e}")
        traceback.print_exc()
        return False

def test_memory_and_performance():
    """Test memory usage and performance edge cases."""
    print("\nTesting memory and performance...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        agent = Agent(model_manager, conv_manager)
        
        # Test creating many conversations
        try:
            for i in range(10):
                conv = conv_manager.new_conversation(f"Test Conversation {i}")
                conv.add_message("user", f"Message {i}")
                conv_manager.save_conversation()
            print("✓ Multiple conversations handled")
        except Exception as e:
            print(f"? Multiple conversations error: {type(e).__name__}")
        
        # Test large message history
        try:
            conv = conv_manager.new_conversation("Large History Test")
            for i in range(50):
                conv.add_message("user", f"User message {i}")
                conv.add_message("assistant", f"Assistant response {i}")
            conv_manager.save_conversation()
            print("✓ Large message history handled")
        except Exception as e:
            print(f"? Large history error: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Memory and performance test failed: {e}")
        traceback.print_exc()
        return False

def test_concurrent_access():
    """Test concurrent access scenarios."""
    print("\nTesting concurrent access...")
    
    try:
        from conversation import ConversationManager
        
        # Test multiple managers accessing same directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            try:
                manager1 = ConversationManager(temp_path)
                manager2 = ConversationManager(temp_path)
                
                conv1 = manager1.new_conversation("Concurrent Test 1")
                conv2 = manager2.new_conversation("Concurrent Test 2")
                
                conv1.add_message("user", "Message from manager 1")
                conv2.add_message("user", "Message from manager 2")
                
                manager1.save_conversation()
                manager2.save_conversation()
                
                print("✓ Concurrent access handled")
            except Exception as e:
                print(f"? Concurrent access error: {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Concurrent access test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all error handling and edge case tests."""
    print("=" * 60)
    print("ERROR HANDLING AND EDGE CASES TESTING")
    print("=" * 60)
    
    tests = [
        test_agent_with_invalid_inputs,
        test_file_operations_errors,
        test_model_manager_errors,
        test_conversation_manager_errors,
        test_tool_execution_errors,
        test_config_errors,
        test_memory_and_performance,
        test_concurrent_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All error handling tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
