#!/usr/bin/env python3
"""
Test script for Core Components functionality.
"""

import os
import sys
import time
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_ai_code_assistant():
    """Test AI Code Assistant functionality."""
    print("Testing AI Code Assistant...")
    
    try:
        from core.ai_code_assistant import AICodeAssistant, AssistantRequest, AssistantResponse
        from models import ModelManager
        
        # Create components
        model_manager = ModelManager()
        workspace_dir = Path('.')
        
        # Create AI Code Assistant
        assistant = AICodeAssistant(model_manager, workspace_dir)
        print("✓ AI Code Assistant created successfully")
        
        # Test request/response classes
        request = AssistantRequest(
            request_id="test_001",
            request_type="analyze",
            code="print('hello world')",
            language="python",
            prompt=None,
            context={},
            preferences={},
            constraints=[]
        )
        print("✓ AssistantRequest created successfully")
        
        return True
    except Exception as e:
        print(f"✗ AI Code Assistant test failed: {e}")
        traceback.print_exc()
        return False

def test_execution_monitor():
    """Test Execution Monitor functionality."""
    print("\nTesting Execution Monitor...")
    
    try:
        from core.execution_monitor import ExecutionMonitor
        
        # Create execution monitor
        monitor = ExecutionMonitor()
        print("✓ ExecutionMonitor created successfully")
        
        # Test basic functionality
        if hasattr(monitor, 'start_monitoring'):
            print("✓ start_monitoring method available")
        if hasattr(monitor, 'stop_monitoring'):
            print("✓ stop_monitoring method available")
        
        return True
    except Exception as e:
        print(f"✗ Execution Monitor test failed: {e}")
        traceback.print_exc()
        return False

def test_rag_system():
    """Test RAG (Retrieval-Augmented Generation) system."""
    print("\nTesting RAG system...")
    
    try:
        from tools.rag import RagTool

        # Create RAG tool
        rag_tool = RagTool()
        print("✓ RAG tool created successfully")
        
        # Test basic functionality
        if hasattr(rag_tool, 'query'):
            print("✓ query method available")
        if hasattr(rag_tool, 'add_documents'):
            print("✓ add_documents method available")
        
        return True
    except Exception as e:
        print(f"✗ RAG system test failed: {e}")
        traceback.print_exc()
        return False

def test_performance_monitoring():
    """Test performance monitoring components."""
    print("\nTesting performance monitoring...")
    
    try:
        from core.enhanced_performance_monitor import EnhancedPerformanceMonitor

        # Create performance monitor
        monitor = EnhancedPerformanceMonitor(Path('.'))
        print("✓ Enhanced Performance Monitor created successfully")
        
        # Test basic functionality
        if hasattr(monitor, 'start_monitoring'):
            print("✓ start_monitoring method available")
        if hasattr(monitor, 'get_metrics'):
            print("✓ get_metrics method available")
        
        return True
    except Exception as e:
        print(f"✗ Performance monitoring test failed: {e}")
        traceback.print_exc()
        return False

def test_learning_system():
    """Test learning system functionality."""
    print("\nTesting learning system...")
    
    try:
        from core.learning_system import LearningSystem

        # Create learning system
        learning_system = LearningSystem(Path('.'))
        print("✓ Learning system created successfully")
        
        # Test basic functionality
        if hasattr(learning_system, 'learn'):
            print("✓ learn method available")
        if hasattr(learning_system, 'get_insights'):
            print("✓ get_insights method available")
        
        return True
    except Exception as e:
        print(f"✗ Learning system test failed: {e}")
        traceback.print_exc()
        return False

def test_optimization_engine():
    """Test optimization engine functionality."""
    print("\nTesting optimization engine...")
    
    try:
        from core.optimization_engine import OptimizationEngine
        from models import ModelManager

        # Create optimization engine
        model_manager = ModelManager()
        optimizer = OptimizationEngine(model_manager, Path('.'))
        print("✓ Optimization engine created successfully")
        
        # Test basic functionality
        if hasattr(optimizer, 'optimize'):
            print("✓ optimize method available")
        if hasattr(optimizer, 'analyze_performance'):
            print("✓ analyze_performance method available")
        
        return True
    except Exception as e:
        print(f"✗ Optimization engine test failed: {e}")
        traceback.print_exc()
        return False

def test_intelligent_refactoring():
    """Test intelligent refactoring functionality."""
    print("\nTesting intelligent refactoring...")
    
    try:
        from core.intelligent_refactoring import IntelligentRefactoring

        # Create refactoring system
        refactoring = IntelligentRefactoring(Path('.'))
        print("✓ Intelligent refactoring created successfully")
        
        # Test basic functionality
        if hasattr(refactoring, 'analyze_code'):
            print("✓ analyze_code method available")
        if hasattr(refactoring, 'suggest_improvements'):
            print("✓ suggest_improvements method available")
        
        return True
    except Exception as e:
        print(f"✗ Intelligent refactoring test failed: {e}")
        traceback.print_exc()
        return False

def test_enhanced_agent():
    """Test enhanced agent functionality."""
    print("\nTesting enhanced agent...")
    
    try:
        from core.enhanced_agent import EnhancedAgent, AgentMode, TaskType
        
        # Test enums
        print(f"✓ AgentMode enum available: {list(AgentMode)}")
        print(f"✓ TaskType enum available: {list(TaskType)}")
        
        # Create enhanced agent (this might fail due to dependencies)
        try:
            enhanced_agent = EnhancedAgent()
            print("✓ Enhanced agent created successfully")
        except Exception as e:
            print(f"? Enhanced agent creation failed (expected): {type(e).__name__}")
        
        return True
    except Exception as e:
        print(f"✗ Enhanced agent test failed: {e}")
        traceback.print_exc()
        return False

def test_agent_rag_integration():
    """Test Agent's RAG tool integration."""
    print("\nTesting Agent RAG integration...")
    
    try:
        from agent import Agent
        from models import ModelManager
        from conversation import ConversationManager
        
        # Create components
        model_manager = ModelManager()
        conv_manager = ConversationManager(Path('conversations'))
        
        # Create agent
        agent = Agent(model_manager, conv_manager)
        
        # Check if RAG tool is available
        if 'rag' in agent.tools:
            print("✓ RAG tool available in agent")
        else:
            print("? RAG tool not available (might be disabled)")
        
        return True
    except Exception as e:
        print(f"✗ Agent RAG integration test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all core component tests."""
    print("=" * 60)
    print("CORE COMPONENTS TESTING")
    print("=" * 60)
    
    tests = [
        test_ai_code_assistant,
        test_execution_monitor,
        test_rag_system,
        test_performance_monitoring,
        test_learning_system,
        test_optimization_engine,
        test_intelligent_refactoring,
        test_enhanced_agent,
        test_agent_rag_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All core component tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
