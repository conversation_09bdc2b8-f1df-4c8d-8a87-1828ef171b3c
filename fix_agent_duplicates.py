#!/usr/bin/env python3
"""
Script to fix duplicate method definitions in agent.py
"""

import re
from pathlib import Path

def fix_agent_duplicates():
    """Remove duplicate method definitions from agent.py"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track methods we've seen
    seen_methods = set()
    lines = content.split('\n')
    output_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Check if this is a method definition
        method_match = re.match(r'    def (_register_\w+_tools|_execute_\w+)\(', line)
        if method_match:
            method_name = method_match.group(1)
            
            if method_name in seen_methods:
                print(f"Found duplicate method: {method_name} at line {i+1}")
                
                # Skip this entire method definition
                indent_level = len(line) - len(line.lstrip())
                i += 1
                
                # Skip until we find the next method or class definition at the same or lower indent level
                while i < len(lines):
                    current_line = lines[i]
                    if current_line.strip() == "":
                        i += 1
                        continue
                    
                    current_indent = len(current_line) - len(current_line.lstrip())
                    
                    # If we find a line at the same or lower indent level that's a method/class def, stop
                    if (current_indent <= indent_level and 
                        (current_line.strip().startswith('def ') or 
                         current_line.strip().startswith('class ') or
                         current_line.strip().startswith('if ') or
                         current_line.strip().startswith('else:') or
                         current_line.strip().startswith('elif ') or
                         current_line.strip().startswith('try:') or
                         current_line.strip().startswith('except ') or
                         current_line.strip().startswith('finally:') or
                         current_line.strip().startswith('for ') or
                         current_line.strip().startswith('while ') or
                         current_line.strip().startswith('with '))):
                        break
                    i += 1
                
                # Don't increment i again, we're already at the next line
                continue
            else:
                seen_methods.add(method_name)
                print(f"Keeping method: {method_name} at line {i+1}")
        
        output_lines.append(line)
        i += 1
    
    # Write the cleaned content back
    cleaned_content = '\n'.join(output_lines)
    
    # Create backup
    backup_file = agent_file.with_suffix('.py.backup')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Backup created: {backup_file}")
    
    # Write cleaned file
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print(f"Cleaned agent.py - removed duplicates")
    print(f"Original lines: {len(lines)}")
    print(f"Cleaned lines: {len(output_lines)}")
    print(f"Lines removed: {len(lines) - len(output_lines)}")
    
    return True

if __name__ == "__main__":
    success = fix_agent_duplicates()
    if success:
        print("✓ Successfully cleaned agent.py")
    else:
        print("✗ Failed to clean agent.py")
