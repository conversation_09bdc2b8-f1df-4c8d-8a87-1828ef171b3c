"""
Model manager for the Advanced AI Agent.
"""

from typing import Dict, List, Optional, Any, Generator, Union

from PIL import Image

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from models.gemini import GeminiModel
from config import get_api_key, get_available_models

class ModelManager:
    """Model manager for the Advanced AI Agent."""

    def __init__(
        self,
        provider: str = "gemini",
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 4096,
        api_key: Optional[str] = None
    ):
        """Initialize the model manager.

        Args:
            provider: The provider to use.
            model_name: The name of the model to use. If None, will use the default for the provider.
            temperature: The temperature to use for generation.
            max_tokens: The maximum number of tokens to generate.
            api_key: The API key to use. If None, will try to get from config.
        """
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Get available models
        self.available_models = get_available_models()

        # Set default model name if not provided
        if model_name is None:
            if provider in self.available_models and self.available_models[provider]:
                model_name = self.available_models[provider][0]
            else:
                raise ValueError(f"No models available for provider {provider}. Available: {self.available_models}")

        self.model_name = model_name

        # Initialize the model based on the provider
        if provider == "gemini":
            # Use provided API key or get from config
            model_api_key = api_key if api_key is not None else get_api_key("gemini")
            self.model = GeminiModel(
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                api_key=model_api_key
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        return self.model.generate(prompt, system_prompt, images, conversation_history)

    def chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        return self.model.chat_generate(message, images, conversation_history)

    def stream_generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        yield from self.model.stream_generate(prompt, system_prompt, images, conversation_history)

    def stream_chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        yield from self.model.stream_chat_generate(message, images, conversation_history)

    def generate_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate a response to a prompt (alias for generate).

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.

        Returns:
            The generated response.
        """
        return self.generate(prompt, system_prompt)

    def generate_streaming_response(self, prompt: str, system_prompt: Optional[str] = None) -> Generator[str, None, None]:
        """Stream a response to a prompt (alias for stream_generate).

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.

        Yields:
            Chunks of the generated response.
        """
        yield from self.stream_generate(prompt, system_prompt)

    def set_api_key(self, api_key: str) -> None:
        """Set the API key for the model.

        Args:
            api_key: The API key to set.
        """
        if hasattr(self.model, 'set_api_key'):
            self.model.set_api_key(api_key)
        else:
            # Recreate the model with the new API key
            if self.provider == "gemini":
                self.model = GeminiModel(
                    model_name=self.model_name,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    api_key=api_key
                )

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model.

        Returns:
            Dictionary containing model information.
        """
        return {
            "provider": self.provider,
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "available_models": self.available_models
        }
