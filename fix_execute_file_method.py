#!/usr/bin/env python3
"""
Script to fix the _execute_file method indentation
"""

from pathlib import Path

def fix_execute_file_method():
    """Fix the _execute_file method indentation"""

    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False

    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Find the _execute_file method and fix its indentation
    fixed_lines = []
    in_execute_file_method = False
    method_start_line = None

    for i, line in enumerate(lines):
        if '    def _execute_file(self, args: str) -> str:' in line:
            in_execute_file_method = True
            method_start_line = i
            fixed_lines.append(line)  # Method definition is already correct
            continue

        if in_execute_file_method:
            # Check if we've reached the next method (properly indented class method)
            if (line.strip().startswith('def ') and
                line.startswith('    def ') and
                '_execute_file' not in line):
                # We've reached the next method
                in_execute_file_method = False
                fixed_lines.append(line)
                continue

            # Check if we've reached end of file or a top-level definition
            if (line.strip() and
                not line.startswith(' ') and
                not line.startswith('\t') and
                not line.strip().startswith('#')):
                # End of class/file
                in_execute_file_method = False
                fixed_lines.append(line)
                continue

            # Fix indentation for method content
            if line.strip():  # Non-empty line
                # Determine proper indentation level
                if line.strip().startswith('"""') or line.strip().startswith('Args:') or line.strip().startswith('Returns:'):
                    # Docstring content should be 8 spaces
                    if not line.startswith('        '):
                        fixed_line = '        ' + line.lstrip()
                        print(f"Fixed docstring line {i+1}: {line.strip()}")
                        fixed_lines.append(fixed_line)
                    else:
                        fixed_lines.append(line)
                elif (line.strip().startswith('if ') or line.strip().startswith('elif ') or
                      line.strip().startswith('else:') or line.strip().startswith('try:') or
                      line.strip().startswith('except') or line.strip().startswith('for ') or
                      line.strip().startswith('while ') or line.strip().startswith('with ') or
                      line.strip().startswith('return ') or line.strip().startswith('#') or
                      '=' in line.strip()):
                    # Method body content should be 8 spaces
                    if not line.startswith('        '):
                        fixed_line = '        ' + line.lstrip()
                        print(f"Fixed method body line {i+1}: {line.strip()}")
                        fixed_lines.append(fixed_line)
                    else:
                        fixed_lines.append(line)
                else:
                    # Other content, check current indentation and fix if needed
                    if not line.startswith('        ') and line.strip():
                        fixed_line = '        ' + line.lstrip()
                        print(f"Fixed content line {i+1}: {line.strip()}")
                        fixed_lines.append(fixed_line)
                    else:
                        fixed_lines.append(line)
            else:
                # Empty line, keep as is
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)

    # Write the fixed content back
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)

    print(f"Fixed _execute_file method indentation")
    return True

if __name__ == "__main__":
    success = fix_execute_file_method()
    if success:
        print("✓ Successfully fixed _execute_file method")
    else:
        print("✗ Failed to fix _execute_file method")
