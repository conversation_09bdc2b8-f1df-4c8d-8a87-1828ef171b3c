#!/usr/bin/env python3
"""
Test script for Configuration System functionality.
"""

import os
import sys
import time
import traceback
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_config_imports():
    """Test configuration imports."""
    print("Testing configuration imports...")
    
    try:
        from config import (
            Config, load_config, save_config, get_api_key, set_api_key,
            get_available_models, AVAILABLE_MODELS
        )
        print("✓ All config imports successful")
        return True
    except Exception as e:
        print(f"✗ Config imports failed: {e}")
        traceback.print_exc()
        return False

def test_config_class():
    """Test Config class functionality."""
    print("\nTesting Config class...")
    
    try:
        from config import Config
        
        # Create a config instance
        config = Config()
        print("✓ Config instance created successfully")
        
        # Test default values
        print(f"✓ Default model: {config.agent.model}")
        print(f"✓ Default provider: {config.agent.provider}")
        print(f"✓ Default temperature: {config.agent.temperature}")
        print(f"✓ Default max_tokens: {config.agent.max_tokens}")

        # Test modifying values
        config.agent.model = "test-model"
        config.agent.temperature = 0.5
        print("✓ Config values can be modified")
        
        return True
    except Exception as e:
        print(f"✗ Config class test failed: {e}")
        traceback.print_exc()
        return False

def test_config_loading():
    """Test configuration loading."""
    print("\nTesting configuration loading...")
    
    try:
        from config import load_config
        
        # Load default config
        config = load_config()
        print("✓ Default config loaded successfully")
        
        # Check config attributes
        agent_attrs = ['model', 'provider', 'temperature', 'max_tokens']
        for attr in agent_attrs:
            if hasattr(config.agent, attr):
                print(f"✓ Config.agent has {attr}: {getattr(config.agent, attr)}")
            else:
                print(f"✗ Config.agent missing {attr}")

        config_attrs = ['debug', 'api_keys', 'history_dir']
        for attr in config_attrs:
            if hasattr(config, attr):
                print(f"✓ Config has {attr}: {getattr(config, attr)}")
            else:
                print(f"✗ Config missing {attr}")
        
        return True
    except Exception as e:
        print(f"✗ Config loading test failed: {e}")
        traceback.print_exc()
        return False

def test_config_saving():
    """Test configuration saving."""
    print("\nTesting configuration saving...")
    
    try:
        from config import load_config, save_config
        
        # Load config
        config = load_config()
        original_model = config.agent.model

        # Modify config
        config.agent.model = "test-save-model"

        # Save config
        save_config(config)
        print("✓ Config saved successfully")

        # Load again to verify
        new_config = load_config()
        if new_config.agent.model == "test-save-model":
            print("✓ Config changes persisted")
        else:
            print(f"? Config changes not persisted: {new_config.agent.model}")

        # Restore original
        config.agent.model = original_model
        save_config(config)
        
        return True
    except Exception as e:
        print(f"✗ Config saving test failed: {e}")
        traceback.print_exc()
        return False

def test_api_key_management():
    """Test API key management."""
    print("\nTesting API key management...")
    
    try:
        from config import get_api_key, set_api_key
        
        # Test getting API key (might be None)
        api_key = get_api_key("gemini")
        print(f"✓ Got API key for gemini: {'Yes' if api_key else 'No'}")
        
        # Test setting API key
        test_key = "test_api_key_12345"
        set_api_key("gemini", test_key)
        print("✓ API key set successfully")
        
        # Test getting the set key
        retrieved_key = get_api_key("gemini")
        if retrieved_key == test_key:
            print("✓ API key retrieved correctly")
        else:
            print(f"? API key mismatch: expected {test_key}, got {retrieved_key}")
        
        # Clean up - restore original key or remove test key
        if api_key:
            set_api_key("gemini", api_key)
        
        return True
    except Exception as e:
        print(f"✗ API key management test failed: {e}")
        traceback.print_exc()
        return False

def test_available_models():
    """Test available models functionality."""
    print("\nTesting available models...")
    
    try:
        from config import get_available_models, AVAILABLE_MODELS
        
        # Test getting available models
        models = get_available_models()
        print(f"✓ Available models retrieved: {len(models)} providers")
        
        # Check if gemini models are available
        if "gemini" in models:
            gemini_models = models["gemini"]
            print(f"✓ Gemini models available: {len(gemini_models)} models")
            print(f"  First few: {gemini_models[:3]}")
        else:
            print("✗ Gemini models not found")
        
        # Test AVAILABLE_MODELS constant
        if AVAILABLE_MODELS:
            print(f"✓ AVAILABLE_MODELS constant defined: {len(AVAILABLE_MODELS)} providers")
        else:
            print("✗ AVAILABLE_MODELS constant not defined")
        
        return True
    except Exception as e:
        print(f"✗ Available models test failed: {e}")
        traceback.print_exc()
        return False

def test_config_directory():
    """Test configuration directory handling."""
    print("\nTesting configuration directory...")
    
    try:
        from config import load_config
        
        # Load config to check directory
        config = load_config()

        # Check history_dir as config directory
        if hasattr(config, 'history_dir'):
            config_dir = Path(config.history_dir).parent  # .advanced_ai_agent directory
            print(f"✓ Config directory: {config_dir}")

            if config_dir.exists():
                print("✓ Config directory exists")
            else:
                print("? Config directory doesn't exist (might be created on save)")
        else:
            print("? Config directory attribute not found")
        
        return True
    except Exception as e:
        print(f"✗ Config directory test failed: {e}")
        traceback.print_exc()
        return False

def test_config_with_custom_path():
    """Test configuration with custom path."""
    print("\nTesting configuration with custom path...")
    
    try:
        from config import Config, save_config, load_config
        
        # Create a temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create config with custom path
            config = Config()
            config.history_dir = temp_path / "history"
            config.agent.model = "custom-path-test-model"
            
            # Save to custom path
            save_config(config)
            print("✓ Config saved to custom path")
            
            # Check if file was created
            config_file = temp_path / "config.json"
            if config_file.exists():
                print("✓ Config file created in custom path")
            else:
                print("? Config file not found in custom path")
        
        return True
    except Exception as e:
        print(f"✗ Custom path test failed: {e}")
        traceback.print_exc()
        return False

def test_config_validation():
    """Test configuration validation."""
    print("\nTesting configuration validation...")
    
    try:
        from config import Config, load_config
        
        # Test with valid values
        config = Config()
        config.agent.temperature = 0.7
        config.agent.max_tokens = 4096
        print("✓ Valid config values accepted")

        # Test edge cases
        config.agent.temperature = 0.0
        config.agent.temperature = 1.0
        print("✓ Temperature edge values accepted")

        config.agent.max_tokens = 1
        config.agent.max_tokens = 100000
        print("✓ Max tokens edge values accepted")
        
        return True
    except Exception as e:
        print(f"✗ Config validation test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all configuration system tests."""
    print("=" * 60)
    print("CONFIGURATION SYSTEM TESTING")
    print("=" * 60)
    
    tests = [
        test_config_imports,
        test_config_class,
        test_config_loading,
        test_config_saving,
        test_api_key_management,
        test_available_models,
        test_config_directory,
        test_config_with_custom_path,
        test_config_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All configuration system tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
